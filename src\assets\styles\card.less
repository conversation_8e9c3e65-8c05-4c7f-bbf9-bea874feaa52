// 卡片基础样式
.card-base {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: #fff;
  overflow: hidden;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(24, 144, 255, 0.2);
  }
}

// 卡片内容区域
.card-content {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 卡片标题区域
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2d3d;
    margin: 0;
  }
  
  .card-extra {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// 卡片页脚
.card-footer {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 无数据状态
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  gap: 16px;
  
  .empty-icon {
    font-size: 48px;
    opacity: 0.6;
    margin-bottom: 8px;
  }
  
  .empty-text {
    font-size: 14px;
    margin-bottom: 16px;
  }
}
