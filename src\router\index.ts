import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import LoginView from '@/views/login.vue'
import NotFound from '@/views/404.vue'

// 项目相关页面
import ProjectOverview from '@/views/project/project_overview.vue'
import ProjectDetail from '@/views/project/project_detail.vue'

// 其他页面
import SystemSettings from '@/views/system/system_settings.vue'
import AgentList from '@/views/agent/list.vue'
import AgentChat from '@/views/agent/chat.vue'
import TaskList from '@/views/task/list.vue'

// 路由元信息类型定义
declare module 'vue-router' {
  interface RouteMeta {
    title: string
    requiresAuth?: boolean
    keepAlive?: boolean
  }
}

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: LoginView,
    meta: { 
      title: '登录',
      requiresAuth: false
    },
    beforeEnter: (to, from, next) => {
      // 如果已经登录，重定向到首页
      const isAuthenticated = localStorage.getItem('token')
      if (isAuthenticated) {
        next('/')
      } else {
        next()
      }
    }
  },
  {
    path: '/',
    component: MainLayout,
    redirect: '/project/overview',
    meta: { requiresAuth: true },
    children: [
      // 项目相关路由
      {
        path: 'project',
        redirect: '/project/overview',
        meta: { title: '项目管理' },
        children: [
          {
            path: 'overview',
            name: 'ProjectOverview',
            component: ProjectOverview,
            meta: { 
              title: '项目概览',
              keepAlive: true
            }
          },
          {
            path: 'detail/:id',
            name: 'ProjectDetail',
            component: ProjectDetail,
            meta: { 
              title: '项目详情',
              keepAlive: false
            },
            props: true,
            redirect: { name: 'ProjectDetailOverview' },
            children: [
              {
                path: 'overview',
                name: 'ProjectDetailOverview',
                component: () => import('@/views/project/detail/overview.vue'),
                meta: { 
                  title: '项目概览',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: 'task',
                name: 'ProjectDetailTask',
                component: () => import('@/views/project/detail/task.vue'),
                meta: { 
                  title: '项目任务',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: 'process',
                name: 'ProjectDetailProcess',
                component: () => import('@/views/project/detail/process.vue'),
                meta: { 
                  title: '项目流程',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: 'document',
                name: 'ProjectDetailDocument',
                component: () => import('@/views/project/detail/document.vue'),
                meta: { 
                  title: '项目文档',
                  keepAlive: true,
                },
                props: true
              },
              {
                path: '',
                redirect: { name: 'ProjectDetailOverview' }
              }
            ]
          }
        ]
      },
      // 任务管理
      {
        path: 'task',
        name: 'Task',
        meta: { title: '任务管理' },
        redirect: '/task/list',
        children: [
          {
            path: 'list',
            name: 'TaskList',
            component: TaskList,
            meta: { title: '任务列表', keepAlive: true }
          }
        ]
      },
      // Agent相关路由
      {
        path: 'agent',
        name: 'Agent',
        redirect: '/agent/list',
        meta: { title: 'AI助手' },
        children: [
          {
            path: 'chat/:id',
            name: 'AgentChat',
            component: AgentChat,
            meta: { title: 'AI对话' },
            props: true
          },
          {
            path: 'list',
            name: 'AgentList',
            component: AgentList,
            meta: { title: 'AI助手列表', keepAlive: true }
          }
        ]
      },
      // 系统设置路由
      {
        path: 'system',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { title: '系统设置' }
      },
      // 重定向根路径
      {
        path: '',
        redirect: '/project/overview'
      }
    ]
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { 
      title: '404 页面不存在',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果路由有 hash，滚动到对应元素
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    // 如果是从浏览器前进/后退按钮触发的导航，则恢复到之前的位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到顶部
    return { top: 0, behavior: 'smooth' }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - PPMS` : 'PPMS'
  
  // 检查是否需要登录
  const isAuthenticated = !!localStorage.getItem('token')
  
  // 如果需要认证但未登录，重定向到登录页
  if (to.meta.requiresAuth && !isAuthenticated) {
    next({ 
      name: 'Login',
      query: { redirect: to.fullPath } // 保存目标路径，登录后可以重定向回来
    })
    return
  }
  
  // 如果已登录但访问登录页，重定向到首页
  if (to.name === 'Login' && isAuthenticated) {
    next('/')
    return
  }
  
  // 继续导航
  next()
})

export default router
