/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArrowDownOutlined: typeof import('@vicons/antd')['ArrowDownOutlined']
    ArrowUpOutlined: typeof import('@vicons/antd')['ArrowUpOutlined']
    BookOutlined: typeof import('@vicons/antd')['BookOutlined']
    IconAntDesignArrowDownOutlined: typeof import('~icons/ant-design/arrow-down-outlined')['default']
    IconAntDesignArrowUpOutlined: typeof import('~icons/ant-design/arrow-up-outlined')['default']
    IconAntDesignBookOutlined: typeof import('~icons/ant-design/book-outlined')['default']
    IconAntDesignDeleteOutlined: typeof import('~icons/ant-design/delete-outlined')['default']
    IconAntDesignEditOutlined: typeof import('~icons/ant-design/edit-outlined')['default']
    IconAntDesignPlusOutlined: typeof import('~icons/ant-design/plus-outlined')['default']
    IconAntDesignStarFilled: typeof import('~icons/ant-design/star-filled')['default']
    IconAntDesignStarOutlined: typeof import('~icons/ant-design/star-outlined')['default']
    IconAntDesignUserOutlined: typeof import('~icons/ant-design/user-outlined')['default']
    NA: typeof import('naive-ui')['NA']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBadge: typeof import('naive-ui')['NBadge']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDivider: typeof import('naive-ui')['NDivider']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NMenu: typeof import('naive-ui')['NMenu']
    NModal: typeof import('naive-ui')['NModal']
    NProgress: typeof import('naive-ui')['NProgress']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTimeline: typeof import('naive-ui')['NTimeline']
    NTimelineItem: typeof import('naive-ui')['NTimelineItem']
    NTooltip: typeof import('naive-ui')['NTooltip']
    PlusOutlined: typeof import('@vicons/antd')['PlusOutlined']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StarFilled: typeof import('@vicons/antd')['StarFilled']
    StarOutlined: typeof import('@vicons/antd')['StarOutlined']
    UserOutlined: typeof import('@vicons/antd')['UserOutlined']
  }
}
