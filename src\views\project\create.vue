<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import PageLayout from '@/layouts/PageLayout.vue'

const router = useRouter()
const message = useMessage()

// 表单数据
const formRef = ref()
const formData = reactive({
  name: '',
  description: '',
  manager: '',
  type: '',
  priority: 'medium',
  startDate: null,
  endDate: null,
  budget: null,
  tags: [],
  cover: ''
})

// 项目类型选项
const projectTypes = [
  { label: '产品研发', value: 'product' },
  { label: '设计', value: 'design' },
  { label: '数据', value: 'data' },
  { label: '运营', value: 'operation' },
  { label: '市场', value: 'marketing' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入项目名称',
    trigger: ['input', 'blur']
  },
  manager: {
    required: true,
    message: '请输入项目经理',
    trigger: ['input', 'blur']
  },
  type: {
    required: true,
    message: '请选择项目类型',
    trigger: ['change', 'blur']
  },
  startDate: {
    required: true,
    message: '请选择开始日期',
    trigger: ['change', 'blur']
  },
  endDate: {
    required: true,
    message: '请选择结束日期',
    trigger: ['change', 'blur']
  }
}

const loading = ref(false)

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('项目创建成功！')
    router.push('/project/overview')
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 重置表单
const handleReset = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    name: '',
    description: '',
    manager: '',
    type: '',
    priority: 'medium',
    startDate: null,
    endDate: null,
    budget: null,
    tags: [],
    cover: ''
  })
}
</script>

<template>
  <page-layout title="新建项目">
    <template #extra>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button @click="handleReset">重置</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          创建项目
        </n-button>
      </n-space>
    </template>

    <n-card>
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="2" :x-gap="24">
          <n-gi>
            <n-form-item label="项目名称" path="name">
              <n-input
                v-model:value="formData.name"
                placeholder="请输入项目名称"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="项目经理" path="manager">
              <n-input
                v-model:value="formData.manager"
                placeholder="请输入项目经理"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="项目类型" path="type">
              <n-select
                v-model:value="formData.type"
                :options="projectTypes"
                placeholder="请选择项目类型"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="优先级" path="priority">
              <n-select
                v-model:value="formData.priority"
                :options="priorityOptions"
                placeholder="请选择优先级"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="开始日期" path="startDate">
              <n-date-picker
                v-model:value="formData.startDate"
                type="date"
                placeholder="请选择开始日期"
                style="width: 100%"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="结束日期" path="endDate">
              <n-date-picker
                v-model:value="formData.endDate"
                type="date"
                placeholder="请选择结束日期"
                style="width: 100%"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="预算">
              <n-input-number
                v-model:value="formData.budget"
                placeholder="请输入预算"
                style="width: 100%"
                :precision="2"
                :show-button="false"
              >
                <template #suffix>万元</template>
              </n-input-number>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="项目标签">
              <n-dynamic-tags v-model:value="formData.tags" />
            </n-form-item>
          </n-gi>
          <n-gi :span="2">
            <n-form-item label="项目描述">
              <n-input
                v-model:value="formData.description"
                type="textarea"
                placeholder="请输入项目描述"
                :rows="4"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi :span="2">
            <n-form-item label="项目封面">
              <n-input
                v-model:value="formData.cover"
                placeholder="请输入封面图片URL"
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
    </n-card>
  </page-layout>
</template>

<style lang="less" scoped>
:deep(.n-card) {
  .card-base();
}

:deep(.n-form-item-label) {
  font-weight: 500;
}
</style>
