<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import PageLayout from '@/layouts/PageLayout.vue'
import type { Project } from '@/types'

const router = useRouter()
const route = useRoute()
const message = useMessage()

// 获取项目ID
const projectId = computed(() => route.params.id as string)

// 表单数据
const formRef = ref()
const formData = reactive<Partial<Project>>({
  id: '',
  name: '',
  description: '',
  manager: '',
  type: undefined,
  priority: 'medium',
  startDate: null,
  endDate: null,
  tags: [],
  cover: '',
  status: 'in_progress'
})

// 项目类型选项
const projectTypes = [
  { label: '产品研发', value: 'product' },
  { label: '设计', value: 'design' },
  { label: '数据', value: 'data' },
  { label: '运营', value: 'operation' },
  { label: '市场', value: 'marketing' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '收尾中', value: 'finishing' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入项目名称',
    trigger: ['input', 'blur']
  },
  manager: {
    required: true,
    message: '请输入项目经理',
    trigger: ['input', 'blur']
  },
  type: {
    required: true,
    message: '请选择项目类型',
    trigger: ['change', 'blur']
  }
}

const loading = ref(false)
const pageLoading = ref(true)

// 加载项目数据
const loadProjectData = async () => {
  try {
    pageLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    const mockData = {
      id: projectId.value,
      name: 'PPMS项目管理系统',
      description: '一个现代化的项目管理系统，支持任务管理、团队协作等功能',
      manager: '张三',
      type: 'product',
      priority: 'high',
      startDate: Date.now() - 30 * 24 * 60 * 60 * 1000,
      endDate: Date.now() + 60 * 24 * 60 * 60 * 1000,
      tags: ['Vue3', 'TypeScript', '项目管理'],
      cover: 'https://p3-pc.douyinpic.com/obj/ies-music/ies-music-common-bv/acp/playlist_v2/PlayListV3/static/cover.png',
      status: 'in_progress'
    }
    
    Object.assign(formData, mockData)
  } catch (error) {
    message.error('加载项目数据失败')
  } finally {
    pageLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('项目更新成功！')
    router.push('/project/overview')
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.back()
}

onMounted(() => {
  loadProjectData()
})
</script>

<template>
  <page-layout title="编辑项目">
    <template #extra>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          保存更改
        </n-button>
      </n-space>
    </template>

    <n-spin :show="pageLoading">
      <n-card>
        <n-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-placement="left"
          label-width="100px"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="2" :x-gap="24">
            <n-gi>
              <n-form-item label="项目名称" path="name">
                <n-input
                  v-model:value="formData.name"
                  placeholder="请输入项目名称"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="项目经理" path="manager">
                <n-input
                  v-model:value="formData.manager"
                  placeholder="请输入项目经理"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="项目类型" path="type">
                <n-select
                  v-model:value="formData.type"
                  :options="projectTypes"
                  placeholder="请选择项目类型"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="优先级" path="priority">
                <n-select
                  v-model:value="formData.priority"
                  :options="priorityOptions"
                  placeholder="请选择优先级"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="项目状态">
                <n-select
                  v-model:value="formData.status"
                  :options="statusOptions"
                  placeholder="请选择项目状态"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item label="开始日期">
                <n-date-picker
                  v-model:value="formData.startDate"
                  type="date"
                  placeholder="请选择开始日期"
                  style="width: 100%"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="结束日期">
                <n-date-picker
                  v-model:value="formData.endDate"
                  type="date"
                  placeholder="请选择结束日期"
                  style="width: 100%"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item label="项目标签">
                <n-dynamic-tags v-model:value="formData.tags" />
              </n-form-item>
            </n-gi>
            <n-gi :span="2">
              <n-form-item label="项目描述">
                <n-input
                  v-model:value="formData.description"
                  type="textarea"
                  placeholder="请输入项目描述"
                  :rows="4"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="2">
              <n-form-item label="项目封面">
                <n-input
                  v-model:value="formData.cover"
                  placeholder="请输入封面图片URL"
                  clearable
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-form>
      </n-card>
    </n-spin>
  </page-layout>
</template>

<style lang="less" scoped>
@import "@/assets/styles/card.less";

:deep(.n-card) {
  .card-base();
}

:deep(.n-form-item-label) {
  font-weight: 500;
}
</style>
