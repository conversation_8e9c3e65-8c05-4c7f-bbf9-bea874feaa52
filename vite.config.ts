import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import UnoCSS from 'unocss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    UnoCSS(),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar'
          ],
          '@vicons/antd': [
            // 常用图标
            'ArrowUpOutlined',
            'ArrowDownOutlined',
            'StarFilled',
            'StarOutlined',
            'UserOutlined',
            'BookOutlined',
            'PlusOutlined',
            'EditOutlined',
            'DeleteOutlined',
            'SearchOutlined',
            'SettingOutlined',
            'HomeOutlined',
            'ProjectOutlined',
            'TeamOutlined',
            'BellOutlined',
            'LogoutOutlined',
            'FileTextOutlined',
            'CheckCircleOutlined',
            'ClockCircleOutlined',
            'ExclamationCircleOutlined',
            'SyncOutlined',
            'CloseCircleOutlined',
            'RightOutlined',
            'CalendarOutlined',
            'CheckCircleFilled',
            'ExclamationCircleFilled',
            'SendOutlined',
            'FullscreenOutlined',
            'FullscreenExitOutlined',
            'MessageOutlined',
            'DashboardOutlined',
            'OrderedListOutlined',
            'RobotOutlined',
            'CodeOutlined',
            'ExperimentOutlined',
            'RocketOutlined',
            'SafetyOutlined',
            'ScheduleOutlined',
            'DeploymentUnitOutlined',
            'UnorderedListOutlined',
            'SelectOutlined'
          ]
        }
      ],
      dts: 'src/auto-imports.d.ts'
    }),
    Components({
      dts: 'src/components.d.ts',
      resolvers: [NaiveUiResolver()]
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
})
